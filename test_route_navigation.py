#!/usr/bin/env python3
"""
测试 route 方法的代码跳转功能
"""

from flask import Blueprint
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    # 模拟 manager 变量，测试类型声明
    manager: Blueprint = Blueprint('test', __name__)

def test_route_navigation():
    """
    测试代码跳转功能
    
    在 VS Code 中测试以下操作：
    1. 将鼠标悬停在下面的 'manager' 上 - 应该显示 Blueprint 类型信息
    2. 按住 Ctrl 并点击 'manager' - 应该跳转到 Blueprint 类定义
    3. 将鼠标悬停在 'route' 上 - 应该显示 route 方法的签名
    4. 按住 Ctrl 并点击 'route' - 应该跳转到 route 方法定义
    """
    
    # 这里模拟实际的路由装饰器使用
    # manager.route("/test", methods=["GET", "POST"])
    
    print("请在 VS Code 中测试以下代码的跳转功能：")
    print("@manager.route('/test', methods=['GET'])")
    print("其中：")
    print("- manager 应该可以跳转到 Blueprint 类")
    print("- route 应该可以跳转到 route 方法")

if __name__ == "__main__":
    test_route_navigation()
