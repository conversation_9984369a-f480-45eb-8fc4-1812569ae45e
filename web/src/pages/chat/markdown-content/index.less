.markdownContentWrapper {
  :global(section.think) {
    padding-left: 10px;
    color: #8b8b8b;
    border-left: 2px solid #d5d3d3;
    margin-bottom: 10px;
    font-size: 12px;
  }
  :global(blockquote) {
    padding-left: 10px;
    border-left: 4px solid #ccc;
  }
}

.referencePopoverWrapper {
  max-width: 50vw;
}

.referenceChunkImage {
  width: 10vw;
  object-fit: contain;
}

.referenceInnerChunkImage {
  display: block;
  object-fit: contain;
  max-width: 100%;
  max-height: 6vh;
}

.referenceImagePreview {
  max-width: 45vw;
  max-height: 45vh;
}
.chunkContentText {
  .chunkText;
  max-height: 45vh;
  overflow-y: auto;
}
.documentLink {
  padding: 0;
}

.referenceIcon {
  padding: 0 6px;
}

.cursor {
  display: inline-block;
  width: 1px;
  height: 16px;
  background-color: black;
  animation: blink 0.6s infinite;
  vertical-align: text-top;
  @keyframes blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}

.fileThumbnail {
  display: inline-block;
  max-width: 40px;
}
