.chatConfigurationDescription {
  font-size: 14px;
}

.variableContainer {
  padding-bottom: 20px;
  .variableAlign {
    text-align: end;
  }

  .variableLabel {
    margin-right: 14px;
  }

  .variableIcon {
    margin-inline-start: 4px;
    color: rgba(0, 0, 0, 0.45);
    cursor: help;
    writing-mode: horizontal-tb;
  }

  .variableTable {
    margin-top: 14px;
  }
  .editableRow {
    :global(.editable-cell) {
      position: relative;
    }

    :global(.editable-cell-value-wrap) {
      padding: 5px 12px;
      cursor: pointer;
      height: 22px !important;
    }
    &:hover {
      :global(.editable-cell-value-wrap) {
        padding: 4px 11px;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
      }
    }
  }
}

.segmentedHidden {
  opacity: 0;
  height: 0;
  width: 0;
  margin: 0;
}

.sliderInputNumber {
  width: 80px;
}
.variableSlider {
  width: 100%;
}
