{"private": true, "author": "bill", "scripts": {"build": "umi build", "dev": "cross-env UMI_DEV_SERVER_COMPRESS=none umi dev", "postinstall": "umi setup", "lint": "umi lint --eslint-only", "prepare": "cd .. && husky web/.husky", "setup": "umi setup", "start": "npm run dev", "test": "jest --no-cache --coverage"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,less,json}": ["prettier --write --ignore-unknown"]}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.46", "@ant-design/pro-layout": "^7.17.16", "@antv/g2": "^5.2.10", "@antv/g6": "^5.0.10", "@hookform/resolvers": "^3.9.1", "@js-preview/excel": "^1.7.8", "@lexical/react": "^0.23.1", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.40.0", "@tanstack/react-query-devtools": "^5.51.5", "@tanstack/react-table": "^8.20.5", "@uiw/react-markdown-preview": "^5.1.3", "@xyflow/react": "^12.3.6", "ahooks": "^3.7.10", "antd": "^5.12.7", "axios": "^1.6.3", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "dayjs": "^1.11.10", "dompurify": "^3.1.6", "eventsource-parser": "^1.1.2", "human-id": "^4.1.1", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^8.0.0", "immer": "^10.1.1", "input-otp": "^1.4.1", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "lexical": "^0.23.1", "lodash": "^4.17.21", "lucide-react": "^0.508.0", "mammoth": "^1.7.2", "next-themes": "^0.4.6", "openai-speech-stream-player": "^1.0.8", "pptx-preview": "^1.0.5", "rc-tween-one": "^3.0.6", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.56.4", "react-i18next": "^14.0.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-pdf-highlighter": "^6.1.0", "react-string-replace": "^1.1.1", "react-syntax-highlighter": "^15.5.0", "react18-json-view": "^0.2.8", "recharts": "^2.12.4", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sonner": "^1.7.4", "tailwind-merge": "^2.5.4", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "umi": "^4.0.90", "umi-request": "^1.4.0", "unist-util-visit-parents": "^6.0.1", "uuid": "^9.0.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@react-dev-inspector/umi4-plugin": "^2.0.1", "@redux-devtools/extension": "^3.3.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.202", "@types/react": "^18.0.33", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.0.11", "@types/react-syntax-highlighter": "^15.5.11", "@types/testing-library__jest-dom": "^6.0.0", "@types/uuid": "^9.0.8", "@types/webpack-env": "^1.18.4", "@umijs/lint": "^4.1.1", "@umijs/plugins": "^4.1.0", "@welldone-software/why-did-you-render": "^8.0.3", "cross-env": "^7.0.3", "html-loader": "^5.1.0", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.7", "prettier": "^3.2.4", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.4.9", "react-dev-inspector": "^2.0.1", "remark-loader": "^6.0.0", "tailwindcss": "^3", "terser-webpack-plugin": "^5.3.11", "ts-node": "^10.9.2", "typescript": "^5.0.3", "umi-plugin-icons": "^0.1.1"}, "engines": {"node": ">=18.20.4"}, "overrides": {"@radix-ui/react-dismissable-layer": "1.1.4"}}