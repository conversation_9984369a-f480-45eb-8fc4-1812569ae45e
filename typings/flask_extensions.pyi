# Flask 扩展类型声明
# 为动态注入的 Flask 组件提供类型信息

from flask import Blueprint
from typing import Any, Callable, TypeVar, Union, List, Optional

T = TypeVar('T', bound=Callable[..., Any])

# 扩展的 Blueprint 类型，包含所有必要的方法签名
class ExtendedBlueprint(Blueprint):
    def route(
        self, 
        rule: str, 
        **options: Any
    ) -> Callable[[T], T]: ...
    
    def before_request(self, f: T) -> T: ...
    def after_request(self, f: T) -> T: ...
    def teardown_request(self, f: T) -> T: ...
    def before_app_request(self, f: T) -> T: ...
    def after_app_request(self, f: T) -> T: ...
    def teardown_app_request(self, f: T) -> T: ...
    def context_processor(self, f: T) -> T: ...
    def app_context_processor(self, f: T) -> T: ...
    def url_value_preprocessor(self, f: T) -> T: ...
    def url_defaults(self, f: T) -> T: ...
    def app_url_value_preprocessor(self, f: T) -> T: ...
    def app_url_defaults(self, f: T) -> T: ...
    def errorhandler(self, code_or_exception: Union[int, Exception]) -> Callable[[T], T]: ...
    def app_errorhandler(self, code_or_exception: Union[int, Exception]) -> Callable[[T], T]: ...

# 全局 manager 变量的类型声明
manager: ExtendedBlueprint
